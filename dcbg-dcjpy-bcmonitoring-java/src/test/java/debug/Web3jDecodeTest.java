package debug;

import java.util.Arrays;
import java.util.List;

import org.junit.Test;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.abi.datatypes.StaticStruct;
import org.web3j.abi.datatypes.Type;

public class Web3jDecodeTest {
    
    @Test
    public void testWeb3jDecode() {
        // Test hex data from AfterBalance event
        String hexData = "0x00000000000000000000000000000000000000000000000000000000000000600000000000000000000000000000000000000000000000000000000000000100747261636549640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000bb800000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000bb9000000000000000000000000000000000000000000000000000000000000006400000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000bb80000000000000000000000000000000000000000000000000000000000000000";
        
        // Define the expected structure: (tuple[] fromAfterBalance, tuple[] toAfterBalance, bytes32 traceId)
        // where tuple is (uint16 zoneId, uint256 balance)
        
        List<TypeReference<?>> outputParameters = Arrays.asList(
            // fromAfterBalance: tuple[]
            new TypeReference<DynamicArray<StaticStruct>>() {},
            // toAfterBalance: tuple[]
            new TypeReference<DynamicArray<StaticStruct>>() {},
            // traceId: bytes32
            new TypeReference<org.web3j.abi.datatypes.generated.Bytes32>() {}
        );
        
        try {
            @SuppressWarnings("unchecked")
            List<Type> decoded = FunctionReturnDecoder.decode(hexData, (List<TypeReference<Type>>) (List<?>) outputParameters);
            
            System.out.println("Decoded " + decoded.size() + " parameters:");
            
            // fromAfterBalance
            if (decoded.size() > 0 && decoded.get(0) instanceof DynamicArray) {
                DynamicArray<?> fromAfterBalance = (DynamicArray<?>) decoded.get(0);
                System.out.println("fromAfterBalance has " + fromAfterBalance.getValue().size() + " tuples:");
                
                List<Type> tuples = (List<Type>) fromAfterBalance.getValue();
                for (int i = 0; i < tuples.size(); i++) {
                    Type tuple = tuples.get(i);
                    System.out.println("  Tuple " + i + ": " + tuple.getClass().getSimpleName());
                    
                    if (tuple instanceof StaticStruct) {
                        StaticStruct struct = (StaticStruct) tuple;
                        List<Type> values = struct.getValue();
                        System.out.println("    zoneId: " + values.get(0).getValue());
                        System.out.println("    balance: " + values.get(1).getValue());
                    }
                }
            }
            
            // toAfterBalance
            if (decoded.size() > 1 && decoded.get(1) instanceof DynamicArray) {
                DynamicArray<?> toAfterBalance = (DynamicArray<?>) decoded.get(1);
                System.out.println("toAfterBalance has " + toAfterBalance.getValue().size() + " tuples:");
                
                List<Type> tuples = (List<Type>) toAfterBalance.getValue();
                for (int i = 0; i < tuples.size(); i++) {
                    Type tuple = tuples.get(i);
                    System.out.println("  Tuple " + i + ": " + tuple.getClass().getSimpleName());
                    
                    if (tuple instanceof StaticStruct) {
                        StaticStruct struct = (StaticStruct) tuple;
                        List<Type> values = struct.getValue();
                        System.out.println("    zoneId: " + values.get(0).getValue());
                        System.out.println("    balance: " + values.get(1).getValue());
                    }
                }
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
